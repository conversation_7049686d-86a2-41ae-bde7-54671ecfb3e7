import (
   "encoding/json"
)

#Error: "5xx" | "4xx"
#ServiceName: "hermod" | "fenrir" | "kata"

#Shape: {
  service: string
  value: number & >= 0
  severity: "critical" | *"warning"
  expr: string
}

#Shape: S={
  _error: #Error
  if _error == "5xx" {
    expr: "select x from (\(S.service)) where 5xx > \(S.value)"
  }
  if _error == "4xx" {
    expr: "select x from (\(S.service)) where 4xx > \(S.value)"
  }
}

#Input: string @tag(input)

let Output = json.Unmarshal(#Input)

alerting: metrics: {
  for k, v in Output {
   [for s in v {
    name: "\(s.service)Percentage\(k)Errors"
    rules: [{
      _error: "\(k)"
      service: s.service
      value: s.value
    } & #Shape]
  }]
  }
}
   
  