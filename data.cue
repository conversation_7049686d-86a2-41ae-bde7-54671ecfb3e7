import (
   "encoding/json"
   "strings"
)

#Error: "5xx" | "4xx"
#ServiceName: "hermod" | "fenrir" | "kata"
#Severity: "critical" | "warning"

#Shape: {
  service: #ServiceName
  value: number & >= 0
  severity?: #Severity
  expr: string
}

#Shape: S={
  _error: #Error
  expr: "select x from (\(S.service)) where \(_error) > \(S.value)"
}

#Input: string @tag(input)

let Output = json.Unmarshal(#Input)

alerting: metrics: {
  for k, v in Output {
   [for s in v {
    name: "\(strings.ToTitle(s.service))Percentage\(k)Errors"
    rules: [{
      _error: "\(k)"
      service: s.service
      severity: *(s.severity & #Severity) | "warning"
      value: s.value
    } & #Shape]
  }]
  }
}
   
  