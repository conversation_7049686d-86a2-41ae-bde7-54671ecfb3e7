{% extends "base.html" %}

{% block title %}Edit Entry - Data Editor{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">Edit {{ error_type }} Entry</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('update_entry', error_type=error_type, index=index) }}">
                    <div class="mb-3">
                        <label for="service" class="form-label">Service Name</label>
                        <select class="form-select" id="service" name="service" required>
                            <option value="">Select a service...</option>
                            <option value="hermod" {{ 'selected' if entry.service == 'hermod' else '' }}>hermod</option>
                            <option value="fenrir" {{ 'selected' if entry.service == 'fenrir' else '' }}>fenrir</option>
                            <option value="kata" {{ 'selected' if entry.service == 'kata' else '' }}>kata</option>
                        </select>
                        <div class="form-text">Choose from the available service names.</div>
                    </div>

                    <div class="mb-3">
                        <label for="threshold" class="form-label">Threshold</label>
                        <input type="number" class="form-control" id="threshold" name="threshold" 
                               value="{{ entry.threshold }}" min="0" required>
                        <div class="form-text">Numeric threshold value (must be 0 or greater).</div>
                    </div>

                    <div class="mb-3">
                        <label for="severity" class="form-label">Severity (Optional)</label>
                        <select class="form-select" id="severity" name="severity">
                            <option value="">No severity specified</option>
                            <option value="warning" {{ 'selected' if entry.severity == 'warning' else '' }}>warning</option>
                            <option value="critical" {{ 'selected' if entry.severity == 'critical' else '' }}>critical</option>
                        </select>
                        <div class="form-text">Optional severity level for this threshold.</div>
                    </div>

                    <div class="mb-3">
                        <label for="slack" class="form-label">Slack Channel (Optional)</label>
                        <input type="text" class="form-control" id="slack" name="slack" 
                               value="{{ entry.slack or '' }}" placeholder="e.g., production">
                        <div class="form-text">Optional Slack channel for notifications.</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">Update Entry</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Current Entry Data</h5>
                <pre class="bg-light p-3 rounded"><code>{{ entry | tojson(indent=2) }}</code></pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}
