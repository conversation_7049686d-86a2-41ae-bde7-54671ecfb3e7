{% extends "base.html" %}

{% block title %}Create Pull Request - Data Editor{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">Create Pull Request</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('create_pr') }}">
                    <div class="mb-3">
                        <label for="branch_name" class="form-label">Branch Name</label>
                        <input type="text" class="form-control" id="branch_name" name="branch_name" 
                               placeholder="e.g., update-thresholds-2024" required>
                        <div class="form-text">
                            Choose a descriptive name for your feature branch. 
                            If left empty, a timestamp-based name will be generated.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="commit_message" class="form-label">Commit Message</label>
                        <input type="text" class="form-control" id="commit_message" name="commit_message" 
                               placeholder="e.g., Update service thresholds for better monitoring" required>
                        <div class="form-text">Describe what changes you made to the data.</div>
                    </div>

                    <div class="mb-3">
                        <label for="pr_title" class="form-label">Pull Request Title (Optional)</label>
                        <input type="text" class="form-control" id="pr_title" name="pr_title" 
                               placeholder="e.g., Update monitoring thresholds">
                        <div class="form-text">Title for the pull request (will use commit message if empty).</div>
                    </div>

                    <div class="mb-3">
                        <label for="pr_description" class="form-label">Pull Request Description (Optional)</label>
                        <textarea class="form-control" id="pr_description" name="pr_description" rows="4"
                                  placeholder="Describe the changes and why they were made..."></textarea>
                        <div class="form-text">Additional context for reviewers.</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('git_status') }}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-success">Create Branch & Push</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-8 offset-md-2">
        <div class="alert alert-info">
            <h5 class="alert-heading">What happens next?</h5>
            <ol class="mb-0">
                <li><strong>Branch Creation:</strong> A new branch will be created from the current branch</li>
                <li><strong>Commit Changes:</strong> Any modifications to data.json will be committed</li>
                <li><strong>Push to Remote:</strong> The branch will be pushed to the remote repository</li>
                <li><strong>PR Link:</strong> You'll get a direct link to create the pull request on GitHub/GitLab</li>
            </ol>
        </div>
    </div>
</div>

<div class="row mt-3">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Current Data Preview</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">This is what will be committed:</p>
                <div class="bg-light p-3 rounded">
                    <small>
                        <strong>File:</strong> data.json<br>
                        <strong>Status:</strong> <span class="text-success">Ready to commit</span>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-generate branch name based on commit message
document.getElementById('commit_message').addEventListener('input', function() {
    const commitMessage = this.value;
    const branchNameField = document.getElementById('branch_name');
    
    if (!branchNameField.value && commitMessage) {
        // Generate branch name from commit message
        const branchName = commitMessage
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .substring(0, 50);
        
        if (branchName) {
            branchNameField.placeholder = `Suggested: ${branchName}`;
        }
    }
});

// Auto-fill PR title from commit message
document.getElementById('commit_message').addEventListener('input', function() {
    const commitMessage = this.value;
    const prTitleField = document.getElementById('pr_title');
    
    if (!prTitleField.value && commitMessage) {
        prTitleField.placeholder = `Suggested: ${commitMessage}`;
    }
});
</script>
{% endblock %}
