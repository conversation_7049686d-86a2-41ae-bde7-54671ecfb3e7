{% extends "base.html" %}

{% block title %}Git Status - Data Editor{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Git Repository Status</h1>
            <a href="{{ url_for('index') }}" class="btn btn-secondary">Back to Data</a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Repository Information</h4>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Current Branch:</strong>
                    <span class="badge bg-primary ms-2">{{ current_branch or 'Unknown' }}</span>
                </div>
                
                {% if remote_url %}
                <div class="mb-3">
                    <strong>Remote URL:</strong>
                    <div class="mt-1">
                        <code class="text-break">{{ remote_url }}</code>
                    </div>
                </div>
                {% else %}
                <div class="mb-3">
                    <div class="alert alert-warning">
                        <strong>No remote repository configured.</strong><br>
                        You'll need to add a remote to push changes and create PRs.
                    </div>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <strong>Working Directory Status:</strong>
                    {% if has_changes %}
                        <span class="badge bg-warning ms-2">Has uncommitted changes</span>
                    {% else %}
                        <span class="badge bg-success ms-2">Clean working directory</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Recent Commits</h4>
            </div>
            <div class="card-body">
                {% if recent_commits %}
                    <div class="list-group list-group-flush">
                        {% for commit in recent_commits %}
                        <div class="list-group-item px-0">
                            <code class="text-muted">{{ commit }}</code>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">No commits found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Actions</h4>
            </div>
            <div class="card-body">
                {% if remote_url %}
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="{{ url_for('create_pr') }}" class="btn btn-success btn-lg">
                            <i class="bi bi-git"></i> Create Pull Request
                        </a>
                        <button class="btn btn-info" onclick="refreshStatus()">
                            <i class="bi bi-arrow-clockwise"></i> Refresh Status
                        </button>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            Creating a PR will:
                            <ul class="mt-2">
                                <li>Create a new branch from {{ current_branch or 'current branch' }}</li>
                                <li>Commit any changes to data.json</li>
                                <li>Push the branch to the remote repository</li>
                                <li>Provide a link to create the pull request</li>
                            </ul>
                        </small>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        <h5 class="alert-heading">Setup Required</h5>
                        <p>To create pull requests, you need to:</p>
                        <ol>
                            <li>Add a remote repository: <code>git remote add origin &lt;repository-url&gt;</code></li>
                            <li>Push your current branch: <code>git push -u origin {{ current_branch or 'main' }}</code></li>
                        </ol>
                        <p class="mb-0">After setting up the remote, refresh this page to enable PR creation.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshStatus() {
    window.location.reload();
}
</script>
{% endblock %}
