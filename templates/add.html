{% extends "base.html" %}

{% block title %}Add Entry - Data Editor{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">Add New {{ error_type }} Entry</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('add_entry', error_type=error_type) }}">
                    <div class="mb-3">
                        <label for="service" class="form-label">Service Name</label>
                        <select class="form-select" id="service" name="service" required>
                            <option value="">Select a service...</option>
                            <option value="hermod">hermod</option>
                            <option value="fenrir">fenrir</option>
                            <option value="kata">kata</option>
                        </select>
                        <div class="form-text">Choose from the available service names.</div>
                    </div>

                    <div class="mb-3">
                        <label for="threshold" class="form-label">Threshold</label>
                        <input type="number" class="form-control" id="threshold" name="threshold" 
                               min="0" required placeholder="Enter threshold value">
                        <div class="form-text">Numeric threshold value (must be 0 or greater).</div>
                    </div>

                    <div class="mb-3">
                        <label for="severity" class="form-label">Severity (Optional)</label>
                        <select class="form-select" id="severity" name="severity">
                            <option value="">No severity specified</option>
                            <option value="warning">warning</option>
                            <option value="critical">critical</option>
                        </select>
                        <div class="form-text">Optional severity level for this threshold.</div>
                    </div>

                    <div class="mb-3">
                        <label for="slack" class="form-label">Slack Channel (Optional)</label>
                        <input type="text" class="form-control" id="slack" name="slack" 
                               placeholder="e.g., production">
                        <div class="form-text">Optional Slack channel for notifications.</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-success">Add Entry</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-8 offset-md-2">
        <div class="alert alert-info">
            <h5 class="alert-heading">Adding {{ error_type }} Entry</h5>
            <p class="mb-0">You're adding a new entry for {{ error_type }} error monitoring. Make sure to set appropriate threshold values based on your service requirements.</p>
        </div>
    </div>
</div>
{% endblock %}
