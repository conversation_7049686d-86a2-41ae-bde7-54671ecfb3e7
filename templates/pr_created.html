{% extends "base.html" %}

{% block title %}Pull Request Created - Data Editor{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="alert alert-success">
            <h4 class="alert-heading">🎉 Branch Successfully Created!</h4>
            <p class="mb-0">Your changes have been committed and pushed to the remote repository.</p>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">Branch Details</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Branch Name:</strong>
                    <span class="badge bg-primary ms-2">{{ branch_name }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Commit Message:</strong>
                    <div class="mt-1">
                        <code>{{ commit_message }}</code>
                    </div>
                </div>
                
                {% if pr_title %}
                <div class="mb-3">
                    <strong>PR Title:</strong>
                    <div class="mt-1">{{ pr_title }}</div>
                </div>
                {% endif %}
                
                {% if pr_description %}
                <div class="mb-3">
                    <strong>PR Description:</strong>
                    <div class="mt-1 bg-light p-2 rounded">{{ pr_description }}</div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if pr_url %}
<div class="row mt-4">
    <div class="col-md-8 offset-md-2">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">🚀 Ready to Create Pull Request</h4>
            </div>
            <div class="card-body">
                <p>Click the button below to create your pull request:</p>
                <div class="d-grid">
                    <a href="{{ pr_url }}" target="_blank" class="btn btn-success btn-lg">
                        <i class="bi bi-box-arrow-up-right"></i> Create Pull Request
                    </a>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        This will open {{ 'GitHub' if 'github.com' in pr_url else 'GitLab' if 'gitlab.com' in pr_url else 'your repository' }} 
                        in a new tab with the pull request form pre-filled.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row mt-4">
    <div class="col-md-8 offset-md-2">
        <div class="alert alert-info">
            <h5 class="alert-heading">Manual PR Creation Required</h5>
            <p>Your branch has been pushed successfully, but we couldn't generate an automatic PR link.</p>
            <p class="mb-0">
                Please visit your repository manually and create a pull request from the 
                <strong>{{ branch_name }}</strong> branch.
            </p>
        </div>
    </div>
</div>
{% endif %}

<div class="row mt-4">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Next Steps</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>For the Pull Request:</h6>
                        <ul class="list-unstyled">
                            <li>✅ Review the changes</li>
                            <li>✅ Add reviewers if needed</li>
                            <li>✅ Add labels/milestones</li>
                            <li>✅ Submit for review</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Continue Working:</h6>
                        <ul class="list-unstyled">
                            <li><a href="{{ url_for('index') }}">← Back to Data Editor</a></li>
                            <li><a href="{{ url_for('git_status') }}">View Git Status</a></li>
                            <li><a href="{{ url_for('create_pr') }}">Create Another PR</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-8 offset-md-2">
        <div class="alert alert-light">
            <h6 class="alert-heading">Git Commands Used:</h6>
            <div class="font-monospace small">
                <div>git checkout -b {{ branch_name }}</div>
                <div>git add data.json</div>
                <div>git commit -m "{{ commit_message }}"</div>
                <div>git push -u origin {{ branch_name }}</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
