{% extends "base.html" %}

{% block title %}Data Editor - Home{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1>Service Configuration Editor</h1>
                <p class="text-muted">Edit service thresholds and configurations for error monitoring</p>
            </div>
            <div>
                <a href="{{ url_for('git_status') }}" class="btn btn-outline-primary">
                    <i class="bi bi-git"></i> Git & PR
                </a>
            </div>
        </div>
    </div>
</div>

{% for error_type, entries in data.items() %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="mb-0">{{ error_type }} Errors</h3>
                <a href="{{ url_for('add_entry_form', error_type=error_type) }}" class="btn btn-success btn-sm">
                    <i class="bi bi-plus"></i> Add Entry
                </a>
            </div>
            <div class="card-body">
                {% if entries %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Service</th>
                                    <th>Threshold</th>
                                    <th>Severity</th>
                                    <th>Slack</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for entry in entries %}
                                <tr>
                                    <td>
                                        <span class="badge bg-primary service-badge">{{ entry.service }}</span>
                                    </td>
                                    <td>{{ entry.threshold }}</td>
                                    <td>
                                        {% if entry.severity %}
                                            <span class="badge bg-{{ 'danger' if entry.severity == 'critical' else 'warning' }}">
                                                {{ entry.severity }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if entry.slack %}
                                            <span class="badge bg-info">{{ entry.slack }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('edit_entry', error_type=error_type, index=loop.index0) }}" 
                                               class="btn btn-outline-primary">Edit</a>
                                            <form method="POST" 
                                                  action="{{ url_for('delete_entry', error_type=error_type, index=loop.index0) }}" 
                                                  style="display: inline;"
                                                  onsubmit="return confirm('Are you sure you want to delete this entry?')">
                                                <button type="submit" class="btn btn-outline-danger">Delete</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">No entries found for {{ error_type }} errors.</p>
                        <a href="{{ url_for('add_entry_form', error_type=error_type) }}" class="btn btn-primary">
                            Add First Entry
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endfor %}

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Raw JSON Data</h5>
                <pre class="bg-light p-3 rounded"><code>{{ data | tojson(indent=2) }}</code></pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}
