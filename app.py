from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import json
import os
from typing import Dict, List, Any

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'  # Change this in production

DATA_FILE = 'data.json'

def load_data() -> Dict[str, List[Dict[str, Any]]]:
    """Load data from JSON file"""
    try:
        with open(DATA_FILE, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        flash('Data file not found!', 'error')
        return {"5xx": [], "4xx": []}
    except json.JSONDecodeError:
        flash('Invalid JSON format in data file!', 'error')
        return {"5xx": [], "4xx": []}

def save_data(data: Dict[str, List[Dict[str, Any]]]) -> bool:
    """Save data to JSON file"""
    try:
        with open(DATA_FILE, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        flash(f'Error saving data: {str(e)}', 'error')
        return False

@app.route('/')
def index():
    """Main page showing all data"""
    data = load_data()
    return render_template('index.html', data=data)

@app.route('/edit/<error_type>/<int:index>')
def edit_entry(error_type: str, index: int):
    """Edit a specific entry"""
    data = load_data()
    
    if error_type not in data:
        flash('Invalid error type!', 'error')
        return redirect(url_for('index'))
    
    if index >= len(data[error_type]):
        flash('Entry not found!', 'error')
        return redirect(url_for('index'))
    
    entry = data[error_type][index]
    return render_template('edit.html', 
                         error_type=error_type, 
                         index=index, 
                         entry=entry)

@app.route('/update/<error_type>/<int:index>', methods=['POST'])
def update_entry(error_type: str, index: int):
    """Update a specific entry"""
    data = load_data()
    
    if error_type not in data or index >= len(data[error_type]):
        flash('Entry not found!', 'error')
        return redirect(url_for('index'))
    
    # Update the entry with form data
    entry = data[error_type][index]
    entry['service'] = request.form.get('service', entry.get('service', ''))
    
    # Handle threshold as integer
    try:
        entry['threshold'] = int(request.form.get('threshold', entry.get('threshold', 0)))
    except ValueError:
        flash('Threshold must be a number!', 'error')
        return redirect(url_for('edit_entry', error_type=error_type, index=index))
    
    # Handle optional fields
    severity = request.form.get('severity')
    if severity:
        entry['severity'] = severity
    elif 'severity' in entry and not severity:
        del entry['severity']
    
    slack = request.form.get('slack')
    if slack:
        entry['slack'] = slack
    elif 'slack' in entry and not slack:
        del entry['slack']
    
    if save_data(data):
        flash('Entry updated successfully!', 'success')
    
    return redirect(url_for('index'))

@app.route('/add/<error_type>')
def add_entry_form(error_type: str):
    """Show form to add new entry"""
    if error_type not in ['4xx', '5xx']:
        flash('Invalid error type!', 'error')
        return redirect(url_for('index'))
    
    return render_template('add.html', error_type=error_type)

@app.route('/add/<error_type>', methods=['POST'])
def add_entry(error_type: str):
    """Add a new entry"""
    data = load_data()
    
    if error_type not in data:
        flash('Invalid error type!', 'error')
        return redirect(url_for('index'))
    
    # Create new entry
    new_entry = {
        'service': request.form.get('service', ''),
    }
    
    # Handle threshold as integer
    try:
        new_entry['threshold'] = int(request.form.get('threshold', 0))
    except ValueError:
        flash('Threshold must be a number!', 'error')
        return redirect(url_for('add_entry_form', error_type=error_type))
    
    # Handle optional fields
    severity = request.form.get('severity')
    if severity:
        new_entry['severity'] = severity
    
    slack = request.form.get('slack')
    if slack:
        new_entry['slack'] = slack
    
    data[error_type].append(new_entry)
    
    if save_data(data):
        flash('Entry added successfully!', 'success')
    
    return redirect(url_for('index'))

@app.route('/delete/<error_type>/<int:index>', methods=['POST'])
def delete_entry(error_type: str, index: int):
    """Delete a specific entry"""
    data = load_data()
    
    if error_type not in data or index >= len(data[error_type]):
        flash('Entry not found!', 'error')
        return redirect(url_for('index'))
    
    del data[error_type][index]
    
    if save_data(data):
        flash('Entry deleted successfully!', 'success')
    
    return redirect(url_for('index'))

if __name__ == '__main__':
    app.run(debug=True)
