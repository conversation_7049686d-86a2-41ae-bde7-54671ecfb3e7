from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import json
import os
import subprocess
import datetime
from typing import Dict, List, Any, Optional

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'  # Change this in production

DATA_FILE = 'data.json'

# Git configuration
GIT_REMOTE_NAME = 'origin'
GIT_BASE_BRANCH = 'main'  # Change to 'master' if that's your default branch

def load_data() -> Dict[str, List[Dict[str, Any]]]:
    """Load data from JSON file"""
    try:
        with open(DATA_FILE, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        flash('Data file not found!', 'error')
        return {"5xx": [], "4xx": []}
    except json.JSONDecodeError:
        flash('Invalid JSON format in data file!', 'error')
        return {"5xx": [], "4xx": []}

def save_data(data: Dict[str, List[Dict[str, Any]]]) -> bool:
    """Save data to JSON file"""
    try:
        with open(DATA_FILE, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        flash(f'Error saving data: {str(e)}', 'error')
        return False

def run_git_command(command: List[str]) -> tuple[bool, str]:
    """Run a git command and return success status and output"""
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            cwd=os.getcwd()
        )
        return result.returncode == 0, result.stdout + result.stderr
    except Exception as e:
        return False, str(e)

def is_git_repo() -> bool:
    """Check if current directory is a git repository"""
    success, _ = run_git_command(['git', 'status'])
    return success

def init_git_repo() -> bool:
    """Initialize git repository if it doesn't exist"""
    if is_git_repo():
        return True

    success, output = run_git_command(['git', 'init'])
    if not success:
        flash(f'Failed to initialize git repository: {output}', 'error')
        return False

    # Set up initial commit if no commits exist
    success, _ = run_git_command(['git', 'log', '--oneline', '-1'])
    if not success:  # No commits exist
        # Add all files and make initial commit
        run_git_command(['git', 'add', '.'])
        run_git_command(['git', 'commit', '-m', 'Initial commit'])

    return True

def get_current_branch() -> Optional[str]:
    """Get the current git branch name"""
    success, output = run_git_command(['git', 'branch', '--show-current'])
    if success:
        return output.strip()
    return None

def create_and_checkout_branch(branch_name: str) -> bool:
    """Create and checkout a new branch"""
    # First, make sure we're on the base branch
    success, _ = run_git_command(['git', 'checkout', GIT_BASE_BRANCH])
    if not success:
        # If base branch doesn't exist, stay on current branch
        pass

    # Pull latest changes if remote exists
    success, _ = run_git_command(['git', 'pull', GIT_REMOTE_NAME, GIT_BASE_BRANCH])

    # Create and checkout new branch
    success, output = run_git_command(['git', 'checkout', '-b', branch_name])
    if not success:
        flash(f'Failed to create branch: {output}', 'error')
        return False

    return True

def commit_changes(message: str) -> bool:
    """Commit current changes"""
    # Add the data file
    success, output = run_git_command(['git', 'add', DATA_FILE])
    if not success:
        flash(f'Failed to add file: {output}', 'error')
        return False

    # Check if there are changes to commit
    success, output = run_git_command(['git', 'diff', '--cached', '--quiet'])
    if success:  # No changes to commit
        flash('No changes to commit', 'warning')
        return False

    # Commit changes
    success, output = run_git_command(['git', 'commit', '-m', message])
    if not success:
        flash(f'Failed to commit: {output}', 'error')
        return False

    return True

def push_branch(branch_name: str) -> bool:
    """Push branch to remote"""
    success, output = run_git_command(['git', 'push', '-u', GIT_REMOTE_NAME, branch_name])
    if not success:
        flash(f'Failed to push branch: {output}', 'error')
        return False

    return True

def get_remote_url() -> Optional[str]:
    """Get the remote repository URL"""
    success, output = run_git_command(['git', 'remote', 'get-url', GIT_REMOTE_NAME])
    if success:
        return output.strip()
    return None

@app.route('/')
def index():
    """Main page showing all data"""
    data = load_data()
    return render_template('index.html', data=data)

@app.route('/edit/<error_type>/<int:index>')
def edit_entry(error_type: str, index: int):
    """Edit a specific entry"""
    data = load_data()
    
    if error_type not in data:
        flash('Invalid error type!', 'error')
        return redirect(url_for('index'))
    
    if index >= len(data[error_type]):
        flash('Entry not found!', 'error')
        return redirect(url_for('index'))
    
    entry = data[error_type][index]
    return render_template('edit.html', 
                         error_type=error_type, 
                         index=index, 
                         entry=entry)

@app.route('/update/<error_type>/<int:index>', methods=['POST'])
def update_entry(error_type: str, index: int):
    """Update a specific entry"""
    data = load_data()
    
    if error_type not in data or index >= len(data[error_type]):
        flash('Entry not found!', 'error')
        return redirect(url_for('index'))
    
    # Update the entry with form data
    entry = data[error_type][index]
    entry['service'] = request.form.get('service', entry.get('service', ''))
    
    # Handle threshold as integer
    try:
        entry['threshold'] = int(request.form.get('threshold', entry.get('threshold', 0)))
    except ValueError:
        flash('Threshold must be a number!', 'error')
        return redirect(url_for('edit_entry', error_type=error_type, index=index))
    
    # Handle optional fields
    severity = request.form.get('severity')
    if severity:
        entry['severity'] = severity
    elif 'severity' in entry and not severity:
        del entry['severity']
    
    slack = request.form.get('slack')
    if slack:
        entry['slack'] = slack
    elif 'slack' in entry and not slack:
        del entry['slack']
    
    if save_data(data):
        flash('Entry updated successfully!', 'success')
    
    return redirect(url_for('index'))

@app.route('/add/<error_type>')
def add_entry_form(error_type: str):
    """Show form to add new entry"""
    if error_type not in ['4xx', '5xx']:
        flash('Invalid error type!', 'error')
        return redirect(url_for('index'))
    
    return render_template('add.html', error_type=error_type)

@app.route('/add/<error_type>', methods=['POST'])
def add_entry(error_type: str):
    """Add a new entry"""
    data = load_data()
    
    if error_type not in data:
        flash('Invalid error type!', 'error')
        return redirect(url_for('index'))
    
    # Create new entry
    new_entry = {
        'service': request.form.get('service', ''),
    }
    
    # Handle threshold as integer
    try:
        new_entry['threshold'] = int(request.form.get('threshold', 0))
    except ValueError:
        flash('Threshold must be a number!', 'error')
        return redirect(url_for('add_entry_form', error_type=error_type))
    
    # Handle optional fields
    severity = request.form.get('severity')
    if severity:
        new_entry['severity'] = severity
    
    slack = request.form.get('slack')
    if slack:
        new_entry['slack'] = slack
    
    data[error_type].append(new_entry)
    
    if save_data(data):
        flash('Entry added successfully!', 'success')
    
    return redirect(url_for('index'))

@app.route('/delete/<error_type>/<int:index>', methods=['POST'])
def delete_entry(error_type: str, index: int):
    """Delete a specific entry"""
    data = load_data()

    if error_type not in data or index >= len(data[error_type]):
        flash('Entry not found!', 'error')
        return redirect(url_for('index'))

    del data[error_type][index]

    if save_data(data):
        flash('Entry deleted successfully!', 'success')

    return redirect(url_for('index'))

@app.route('/git-status')
def git_status():
    """Show git status and options"""
    if not init_git_repo():
        return redirect(url_for('index'))

    current_branch = get_current_branch()
    remote_url = get_remote_url()

    # Get git status
    success, status_output = run_git_command(['git', 'status', '--porcelain'])
    has_changes = success and bool(status_output.strip())

    # Get recent commits
    success, log_output = run_git_command(['git', 'log', '--oneline', '-5'])
    recent_commits = log_output.strip().split('\n') if success and log_output.strip() else []

    return render_template('git_status.html',
                         current_branch=current_branch,
                         remote_url=remote_url,
                         has_changes=has_changes,
                         recent_commits=recent_commits)

@app.route('/create-pr', methods=['GET', 'POST'])
def create_pr():
    """Create a pull request"""
    if request.method == 'GET':
        return render_template('create_pr.html')

    # Initialize git repo if needed
    if not init_git_repo():
        return redirect(url_for('index'))

    # Get form data
    branch_name = request.form.get('branch_name', '').strip()
    commit_message = request.form.get('commit_message', '').strip()
    pr_title = request.form.get('pr_title', '').strip()
    pr_description = request.form.get('pr_description', '').strip()

    if not branch_name or not commit_message:
        flash('Branch name and commit message are required!', 'error')
        return render_template('create_pr.html')

    # Generate branch name if not provided
    if not branch_name:
        timestamp = datetime.datetime.now().strftime('%Y%m%d-%H%M%S')
        branch_name = f'data-update-{timestamp}'

    # Create and checkout branch
    if not create_and_checkout_branch(branch_name):
        return redirect(url_for('git_status'))

    # Commit changes
    if not commit_changes(commit_message):
        return redirect(url_for('git_status'))

    # Push branch
    if not push_branch(branch_name):
        return redirect(url_for('git_status'))

    flash(f'Successfully pushed branch "{branch_name}" to remote!', 'success')

    # Try to generate PR URL (GitHub/GitLab)
    remote_url = get_remote_url()
    pr_url = None

    if remote_url:
        if 'github.com' in remote_url:
            # Convert SSH/HTTPS URL to GitHub PR URL
            if remote_url.startswith('**************:'):
                repo_path = remote_url.replace('**************:', '').replace('.git', '')
            elif 'github.com' in remote_url:
                repo_path = remote_url.split('github.com/')[-1].replace('.git', '')
            else:
                repo_path = None

            if repo_path:
                pr_url = f'https://github.com/{repo_path}/compare/{GIT_BASE_BRANCH}...{branch_name}'

        elif 'gitlab.com' in remote_url:
            # Convert to GitLab MR URL
            if remote_url.startswith('**************:'):
                repo_path = remote_url.replace('**************:', '').replace('.git', '')
            elif 'gitlab.com' in remote_url:
                repo_path = remote_url.split('gitlab.com/')[-1].replace('.git', '')
            else:
                repo_path = None

            if repo_path:
                pr_url = f'https://gitlab.com/{repo_path}/-/merge_requests/new?merge_request[source_branch]={branch_name}&merge_request[target_branch]={GIT_BASE_BRANCH}'

    return render_template('pr_created.html',
                         branch_name=branch_name,
                         commit_message=commit_message,
                         pr_url=pr_url,
                         pr_title=pr_title,
                         pr_description=pr_description)

if __name__ == '__main__':
    app.run(debug=True)
