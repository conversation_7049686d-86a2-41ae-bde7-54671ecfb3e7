# Data.json Flask Editor

A simple Flask web application for editing the `data.json` file through a user-friendly web interface.

## Features

- **View all entries**: See all 4xx and 5xx error configurations in a clean table format
- **Edit entries**: Modify service names, thresholds, severity levels, and Slack channels
- **Add new entries**: Create new monitoring configurations
- **Delete entries**: Remove unwanted configurations
- **Input validation**: Ensures service names match the allowed values (hermod, fenrir, kata)
- **Real-time JSON preview**: See the raw JSON data as you make changes
- **Git Integration**: Commit changes, create branches, and push to remote repositories
- **Pull Request Creation**: Automatically create PRs on GitHub/GitLab with direct links

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

## Running the Application

1. Make sure `data.json` exists in the same directory as `app.py`
2. Run the Flask application:
```bash
python app.py
```
3. Open your browser and go to: `http://localhost:5000`

## Usage

### Main Page
- View all current configurations organized by error type (4xx/5xx)
- See service names, thresholds, severity levels, and Slack channels
- Access edit/delete actions for each entry
- Add new entries using the "Add Entry" buttons

### Editing Entries
- Service names are constrained to: hermod, fenrir, kata
- Thresholds must be numeric values ≥ 0
- Severity is optional (warning or critical)
- Slack channel is optional text field

### Data Validation
- Service names are validated against allowed values
- Thresholds are validated as positive numbers
- Optional fields can be left empty and will be omitted from JSON

### Git Integration
- **Git Status**: View current branch, remote URL, and recent commits
- **Branch Creation**: Automatically create feature branches for changes
- **Commit & Push**: Commit data.json changes and push to remote
- **PR Links**: Generate direct links to create pull requests on GitHub/GitLab
- **Auto-initialization**: Automatically initialize Git repository if needed

## File Structure

```
├── app.py              # Main Flask application
├── data.json           # Data file (automatically read/written)
├── requirements.txt    # Python dependencies
├── templates/
│   ├── base.html      # Base template with Bootstrap styling
│   ├── index.html     # Main page showing all entries
│   ├── edit.html      # Edit form for existing entries
│   ├── add.html       # Add form for new entries
│   ├── git_status.html # Git repository status page
│   ├── create_pr.html  # Pull request creation form
│   └── pr_created.html # PR success page with links
└── README_flask.md    # This file
```

## Security Notes

- Change the `secret_key` in `app.py` for production use
- The application runs in debug mode by default - disable for production
- No authentication is implemented - add as needed for production use

## Customization

- Modify the service name options in the templates and validation logic
- Add additional fields by updating the form templates and route handlers
- Customize styling by modifying the Bootstrap classes or adding custom CSS
